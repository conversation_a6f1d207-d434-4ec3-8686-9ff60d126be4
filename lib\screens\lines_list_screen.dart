// lib/screens/lines_list_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:gal/gal.dart';
import 'package:share_plus/share_plus.dart';

import '../utils/image_cache_manager.dart';
import '../utils/performance_optimizer.dart';
import '../utils/background_manager.dart';
import '../utils/storage_permission_manager.dart';
import '../providers/theme_provider.dart';
import '../providers/favorites_provider.dart';
import '../data/pickup_lines_data.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/enhanced_edit_dialog.dart';

import '../models/styled_text.dart';

class LinesListScreen extends StatefulWidget {
  final String category;
  final String language;
  const LinesListScreen({
    super.key,
    required this.category,
    required this.language,
  });

  @override
  State<LinesListScreen> createState() => _LinesListScreenState();
}

class _LinesListScreenState extends State<LinesListScreen> {
  // Background images managed by BackgroundManager (supports future expansion to 20+)
  final List<String> backgroundImages = BackgroundManager.getAllBackgrounds();

  // Track background index for each card using ValueNotifiers for better performance
  final Map<int, ValueNotifier<int>> cardBackgroundNotifiers = {};
  // Track initial random backgrounds for each post
  final Map<int, String> initialBackgrounds = {};
  // Track styled text for each quote
  final Map<int, StyledText> styledTexts = {};
  DateTime? _lastTapTime; // For throttling rapid taps

  // Pickup lines loaded based on category
  List<String> lines = [];

  void _changeBackground(int index) {
    // Throttle rapid taps to prevent performance issues
    final now = DateTime.now();
    if (_lastTapTime != null &&
        now.difference(_lastTapTime!).inMilliseconds < 200) {
      return; // Ignore rapid taps
    }
    _lastTapTime = now;

    // Play tap sound
    HapticFeedback.lightImpact();

    // Get or create ValueNotifier for this card
    if (!cardBackgroundNotifiers.containsKey(index)) {
      cardBackgroundNotifiers[index] = ValueNotifier<int>(0);
    }

    final notifier = cardBackgroundNotifiers[index]!;
    final currentBg = notifier.value;
    final newBg = (currentBg + 1) % backgroundImages.length;

    // Update only the specific card, not the entire widget
    notifier.value = newBg;
  }

  void _updateQuote(int index, String newQuote, [TextStyle? newStyle]) {
    // Use optimized setState to reduce unnecessary rebuilds
    if (mounted) {
      setState(() {
        lines[index] = newQuote;
        if (newStyle != null) {
          styledTexts[index] = StyledText.fromTextStyle(newQuote, newStyle);
        }
        // Debug output
        if (kDebugMode) {
          print('Updated quote at index $index: $newQuote');
        }
      });
    }
  }

  @override
  void initState() {
    super.initState();
    // Load pickup lines for the specific category and language
    // Create a mutable copy to allow editing
    lines = List<String>.from(
      PickupLinesData.getLinesForCategory(widget.category, widget.language),
    );

    // Assign random initial backgrounds for each post
    _initializeRandomBackgrounds();

    // Preload images when this screen loads (lazy loading)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _preloadImagesLazily();
    });
  }

  void _initializeRandomBackgrounds() {
    for (int i = 0; i < lines.length; i++) {
      // Use post content hash for consistent background per post
      final postBackground = BackgroundManager.getBackgroundForPost(lines[i]);
      initialBackgrounds[i] = postBackground;

      // Initialize background notifier with random starting index
      final backgroundIndex = backgroundImages.indexOf(postBackground);
      cardBackgroundNotifiers[i] = ValueNotifier<int>(
        backgroundIndex >= 0 ? backgroundIndex : 0,
      );
    }
  }

  void _preloadImagesLazily() {
    // Only preload when user actually needs them
    Future.delayed(const Duration(milliseconds: 500), () {
      OptimizedImageCache().preloadBackgroundImages().catchError((error) {
        if (kDebugMode) {
          debugPrint('Background image preload failed: $error');
        }
      });
    });
  }

  @override
  void dispose() {
    // Clean up ValueNotifiers
    for (final notifier in cardBackgroundNotifiers.values) {
      notifier.dispose();
    }
    cardBackgroundNotifiers.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, FavoritesProvider>(
      builder: (context, themeProvider, favoritesProvider, child) {
        return Scaffold(
          backgroundColor: themeProvider.isDarkMode
              ? Colors.grey.shade900
              : Colors.white,
          appBar: Custom3DAppBar(title: widget.category),
          body: Container(
            color: themeProvider.isDarkMode
                ? Colors.grey.shade900
                : Colors.white,
            child: PageView.builder(
              itemCount: (lines.length / 2)
                  .ceil(), // Number of pages (2 posts per page)
              itemBuilder: (context, pageIndex) {
                final startIndex = pageIndex * 2;

                return Padding(
                  padding: EdgeInsets.only(left: 16, right: 16, bottom: 16),
                  child: Column(
                    children: [
                      // First post on the page
                      if (startIndex < lines.length)
                        Expanded(child: _buildQuoteCard(startIndex)),

                      // Second post on the page (if exists)
                      if (startIndex + 1 < lines.length)
                        Expanded(child: _buildQuoteCard(startIndex + 1)),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuoteCard(int index) {
    // Ensure background notifier exists (fallback for edge cases)
    cardBackgroundNotifiers.putIfAbsent(index, () {
      // If not initialized, use random background
      final randomBg = BackgroundManager.getBackgroundForPost(lines[index]);
      final bgIndex = backgroundImages.indexOf(randomBg);
      return ValueNotifier<int>(bgIndex >= 0 ? bgIndex : 0);
    });

    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return QuoteCard(
          key: ValueKey('quote_card_${index}_${lines[index].hashCode}'),
          index: index,
          quote: lines[index],
          styledText: styledTexts[index],
          backgroundImages: backgroundImages,
          backgroundNotifier: cardBackgroundNotifiers[index]!,
          onBackgroundChange: () => _changeBackground(index),
          onQuoteUpdate: (newQuote, newStyle) =>
              _updateQuote(index, newQuote, newStyle),
          themeProvider: themeProvider,
        );
      },
    );
  }
}

class QuoteCard extends StatefulWidget {
  final int index;
  final String quote;
  final StyledText? styledText;
  final List<String> backgroundImages;
  final ValueNotifier<int> backgroundNotifier;
  final VoidCallback onBackgroundChange;
  final Function(String, TextStyle?) onQuoteUpdate;
  final ThemeProvider themeProvider;

  const QuoteCard({
    super.key,
    required this.index,
    required this.quote,
    this.styledText,
    required this.backgroundImages,
    required this.backgroundNotifier,
    required this.onBackgroundChange,
    required this.onQuoteUpdate,
    required this.themeProvider,
  });

  @override
  State<QuoteCard> createState() => _QuoteCardState();
}

class _QuoteCardState extends State<QuoteCard> with PerformanceOptimizedMixin {
  // Screenshot controller for this card
  final ScreenshotController _screenshotController = ScreenshotController();

  // Save image to gallery with persistent permission handling
  Future<void> _saveToGallery() async {
    try {
      final permissionManager = StoragePermissionManager.instance;

      // Check if we already have permission
      bool hasPermission = await permissionManager.hasStoragePermission();

      // Request permission if we don't have it
      if (!hasPermission) {
        hasPermission = await permissionManager.requestStoragePermission();
      }

      if (hasPermission) {
        // Capture screenshot
        final Uint8List? imageBytes = await _screenshotController.capture();

        if (imageBytes != null) {
          // Save to gallery using Gal
          try {
            await Gal.putImageBytes(
              imageBytes,
              name: "charm_shot_${DateTime.now().millisecondsSinceEpoch}",
            );

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Image saved to gallery!'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to save image: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Storage permission required to save images'),
              backgroundColor: Colors.orange,
              action: SnackBarAction(
                label: 'Settings',
                onPressed: () => permissionManager.openAppSettings(),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  static final _overlayColor = Colors.black.withValues(alpha: 0.4);

  @override
  Widget build(BuildContext context) {
    // Monitor build performance
    final stopwatch = Stopwatch()..start();

    final result = ValueListenableBuilder<int>(
      valueListenable: widget.backgroundNotifier,
      builder: (context, currentBackgroundIndex, child) {
        String currentBg =
            widget.backgroundImages[currentBackgroundIndex %
                widget.backgroundImages.length];

        return Consumer<FavoritesProvider>(
          builder: (context, favoritesProvider, child) {
            return Container(
              margin: EdgeInsets.symmetric(
                vertical: 6,
                horizontal: 8,
              ), // Reduced spacing between posts
              child: Stack(
                children: [
                  // Main post container with integrated buttons
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        // Optimized 3D shadow effect
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.15),
                          spreadRadius: 0,
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.08),
                          spreadRadius: 0,
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Screenshot area with edit button overlay
                        Stack(
                          children: [
                            // Screenshot area - only quote and background
                            GestureDetector(
                              onTap: () {
                                // Make post reactive - change background and play sound
                                widget.onBackgroundChange();
                                if (favoritesProvider.tapSoundEnabled) {
                                  SystemSound.play(SystemSoundType.click);
                                }
                              },
                              child: Screenshot(
                                controller: _screenshotController,
                                child: Container(
                                  height:
                                      320, // Only the quote and background area
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(24),
                                      topRight: Radius.circular(24),
                                    ),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(24),
                                      topRight: Radius.circular(24),
                                    ),
                                    child: Stack(
                                      children: [
                                        // Background Image - Optimized with no fade animation
                                        Positioned.fill(
                                          child: OptimizedAssetImage(
                                            imagePath: currentBg,
                                            fit: BoxFit.cover,
                                            filterQuality: FilterQuality.low,
                                            enableFadeIn:
                                                false, // Disable fade for faster loading
                                            errorWidget: Container(
                                              decoration: BoxDecoration(
                                                gradient: LinearGradient(
                                                  begin: Alignment.topLeft,
                                                  end: Alignment.bottomRight,
                                                  colors: widget
                                                      .themeProvider
                                                      .premiumGradientAccent,
                                                ),
                                              ),
                                              child: Center(
                                                child: Icon(
                                                  Icons.favorite,
                                                  color: Colors.white54,
                                                  size: 48,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        // Dark overlay for better text readability
                                        Container(
                                          width: double.infinity,
                                          height: double.infinity,
                                          color: _overlayColor,
                                        ),
                                        // Content - matching Today's Shot design exactly
                                        Positioned.fill(
                                          child: GestureDetector(
                                            onTap: widget.onBackgroundChange,
                                            child: Container(
                                              padding: EdgeInsets.all(24),
                                              child: Column(
                                                mainAxisSize: MainAxisSize.max,
                                                children: [
                                                  // Quote content - matching Today's Shot exactly
                                                  Expanded(
                                                    child: Container(
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                            vertical: 20,
                                                            horizontal: 16,
                                                          ),
                                                      child: Stack(
                                                        children: [
                                                          // Edit button (preserved from original)
                                                          Positioned(
                                                            top: -5,
                                                            right: -5,
                                                            child: GestureDetector(
                                                              onTap: () {
                                                                showEnhancedEditDialog(
                                                                  context:
                                                                      context,
                                                                  initialText:
                                                                      widget
                                                                          .quote,
                                                                  onSave:
                                                                      (
                                                                        newText,
                                                                        newStyle,
                                                                      ) {
                                                                        widget.onQuoteUpdate(
                                                                          newText,
                                                                          newStyle,
                                                                        );
                                                                      },
                                                                );
                                                              },
                                                              child: Container(
                                                                width: 40,
                                                                height: 40,
                                                                decoration: BoxDecoration(
                                                                  color: Colors
                                                                      .white,
                                                                  shape: BoxShape
                                                                      .circle,
                                                                  boxShadow: [
                                                                    BoxShadow(
                                                                      color: Colors
                                                                          .black
                                                                          .withValues(
                                                                            alpha:
                                                                                0.2,
                                                                          ),
                                                                      spreadRadius:
                                                                          1,
                                                                      blurRadius:
                                                                          4,
                                                                      offset:
                                                                          const Offset(
                                                                            0,
                                                                            2,
                                                                          ),
                                                                    ),
                                                                  ],
                                                                ),
                                                                child: Icon(
                                                                  Icons.edit,
                                                                  color: Colors
                                                                      .black,
                                                                  size: 20,
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                          // Quote text - matching Today's Shot exactly
                                                          Center(
                                                            child: RichText(
                                                              textAlign:
                                                                  TextAlign
                                                                      .center,
                                                              text: TextSpan(
                                                                style: TextStyle(
                                                                  fontSize: 20,
                                                                  color: Colors
                                                                      .white,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .bold,
                                                                  height: 1.4,
                                                                  shadows: [
                                                                    Shadow(
                                                                      color: Colors
                                                                          .black
                                                                          .withValues(
                                                                            alpha:
                                                                                0.5,
                                                                          ),
                                                                      offset:
                                                                          Offset(
                                                                            1,
                                                                            1,
                                                                          ),
                                                                      blurRadius:
                                                                          2,
                                                                    ),
                                                                  ],
                                                                ),
                                                                children: [
                                                                  TextSpan(
                                                                    text: '"',
                                                                    style: TextStyle(
                                                                      fontSize:
                                                                          28,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w900,
                                                                    ),
                                                                  ),
                                                                  TextSpan(
                                                                    text: widget
                                                                        .quote,
                                                                  ),
                                                                  TextSpan(
                                                                    text: '"',
                                                                    style: TextStyle(
                                                                      fontSize:
                                                                          28,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w900,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                  // Action buttons - seamlessly integrated at bottom
                                                  Container(
                                                    decoration: BoxDecoration(
                                                      color: Colors.white,
                                                      borderRadius:
                                                          BorderRadius.only(
                                                            bottomLeft:
                                                                Radius.circular(
                                                                  24,
                                                                ),
                                                            bottomRight:
                                                                Radius.circular(
                                                                  24,
                                                                ),
                                                          ),
                                                      // Subtle inner shadow for depth
                                                      boxShadow: [
                                                        BoxShadow(
                                                          color: Colors.black
                                                              .withValues(
                                                                alpha: 0.05,
                                                              ),
                                                          spreadRadius: 0,
                                                          blurRadius: 2,
                                                          offset: const Offset(
                                                            0,
                                                            -1,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                    padding:
                                                        const EdgeInsets.symmetric(
                                                          horizontal: 16,
                                                          vertical: 14,
                                                        ),
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceEvenly,
                                                      children: [
                                                        _buildSimpleActionButton(
                                                          icon: Icons
                                                              .favorite_border,
                                                          label: 'Like',
                                                          onTap: () {
                                                            final postId =
                                                                'quote_${widget.index}';
                                                            final currentBg =
                                                                widget
                                                                    .backgroundImages[widget
                                                                    .backgroundNotifier
                                                                    .value];
                                                            favoritesProvider
                                                                .toggleFavorite(
                                                                  postId,
                                                                  widget.quote,
                                                                  currentBg,
                                                                );
                                                            if (favoritesProvider
                                                                .tapSoundEnabled) {
                                                              SystemSound.play(
                                                                SystemSoundType
                                                                    .click,
                                                              );
                                                            }
                                                          },
                                                        ),
                                                        _buildSimpleActionButton(
                                                          icon: Icons.download,
                                                          label: 'Save',
                                                          onTap: () async {
                                                            await _saveToGallery();
                                                            if (favoritesProvider
                                                                .tapSoundEnabled) {
                                                              SystemSound.play(
                                                                SystemSoundType
                                                                    .click,
                                                              );
                                                            }
                                                          },
                                                        ),
                                                        _buildSimpleActionButton(
                                                          icon: Icons.copy,
                                                          label: 'Copy',
                                                          onTap: () async {
                                                            final scaffoldMessenger =
                                                                ScaffoldMessenger.of(
                                                                  context,
                                                                );
                                                            await Clipboard.setData(
                                                              ClipboardData(
                                                                text: widget
                                                                    .quote,
                                                              ),
                                                            );
                                                            if (favoritesProvider
                                                                .tapSoundEnabled) {
                                                              SystemSound.play(
                                                                SystemSoundType
                                                                    .click,
                                                              );
                                                            }
                                                            if (mounted) {
                                                              scaffoldMessenger.showSnackBar(
                                                                const SnackBar(
                                                                  content: Text(
                                                                    'Quote copied to clipboard!',
                                                                  ),
                                                                  duration:
                                                                      Duration(
                                                                        seconds:
                                                                            1,
                                                                      ),
                                                                ),
                                                              );
                                                            }
                                                          },
                                                        ),
                                                        _buildSimpleActionButton(
                                                          icon: Icons.share,
                                                          label: 'Share',
                                                          onTap: () async {
                                                            await SharePlus
                                                                .instance
                                                                .share(
                                                                  ShareParams(
                                                                    text:
                                                                        '${widget.quote}\n\n💕 Shared from Charm Shots - The ultimate pickup lines app!',
                                                                    subject:
                                                                        'Check out this pickup line!',
                                                                  ),
                                                                );
                                                            if (favoritesProvider
                                                                .tapSoundEnabled) {
                                                              SystemSound.play(
                                                                SystemSoundType
                                                                    .click,
                                                              );
                                                            }
                                                          },
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );

    // Monitor build performance and report frame drops if needed
    stopwatch.stop();
    if (stopwatch.elapsedMilliseconds > 16) {
      // 16ms = 60fps threshold
      reportFrameDropIfNeeded();
      if (kDebugMode) {
        debugPrint(
          '⚠️ QuoteCard build took ${stopwatch.elapsedMilliseconds}ms (>16ms)',
        );
      }
    }

    return result;
  }

  Widget _buildSimpleActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 6),
          padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 12),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(16), // Rounded buttons
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                spreadRadius: 0,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: Colors.black87,
                size: 26,
              ), // Slightly larger icons
              const SizedBox(height: 6),
              Text(
                label,
                style: const TextStyle(
                  color: Colors.black87,
                  fontSize: 13, // Slightly larger text
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
